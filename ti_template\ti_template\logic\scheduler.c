#include "scheduler.h"
#include "user_pid.h"

uint32_t uwTick = 0;    //系统时间
uint8_t task_num = 0;
//任务结构体
typedef struct{
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
}task_t;

//静态任务列表
static task_t scheduler_task[] = {
		{uart0_task,5,0},
//    {encoder_task,10,0}, //编码器运行周期更改时要同步修改SAMPLE_TIME
		{user_gray_task_12channel,2,0}, // 12路传感器任务，2ms周期适合循迹控制
//		{PID_Task,5,0}
};

/**
 * @brief 调度器初始化
 * @param none
 * @return none
 */
void scheduler_init(void)
{
    // 计算任务数组的元素个数，并将结果存储在 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);

		// 初始化12路传感器（替换原有的7路传感器）
		user_gray_init_12channel();

		user_motor_init();
		encoder_config();
		PID_Init();

		// 添加任务状态监控初始化
		my_printf(UART_0_INST,"Scheduler initialized with %d tasks\r\n", task_num);
		my_printf(UART_0_INST,"12-channel gray sensor task period: 2ms\r\n");

}
/**
 * @brief 调度器运行函数
 * @param none
 * @return none
 */
void scheduler_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒） 
        uint32_t now_time = uwTick;

        // 检查当前时间是否达到任务的执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 更新任务的上次运行时间为当前时间
            scheduler_task[i].last_run = now_time;
            // 执行任务函数
            scheduler_task[i].task_func();
        }
    }
}

/**
 * @brief 滴答定时器中断服务函数
 * @param none
 * @return none
 */
void SysTick_Handler(void)
{
    uwTick++;
}
/**
 * @brief DL库延时函数(ms)
 * @param none
 * @return none
 */
void DL_Delay(uint32_t delayMs)
{
    uint32_t startTick = uwTick;

    // 等待指定的毫秒数
    while ((uwTick - startTick) < delayMs) {
        // 可以在这里添加低功耗模式
        // __WFI(); // 等待中断，降低功耗
			;
    }
}

/**
 * @brief 任务状态监控函数
 * @param none
 * @return none
 */
void scheduler_monitor(void)
{
    static uint32_t last_monitor_time = 0;
    const uint32_t monitor_period = 5000; // 5秒监控周期

    if(uwTick - last_monitor_time >= monitor_period)
    {
        last_monitor_time = uwTick;

        my_printf(UART_0_INST,"=== Task Monitor Report ===\r\n");
        my_printf(UART_0_INST,"System uptime: %lu ms\r\n", uwTick);
        my_printf(UART_0_INST,"Active tasks: %d\r\n", task_num);

        for(uint8_t i = 0; i < task_num; i++)
        {
            uint32_t time_since_last = uwTick - scheduler_task[i].last_run;
            my_printf(UART_0_INST,"Task %d: period=%lu ms, last_run=%lu ms ago\r\n",
                i, scheduler_task[i].rate_ms, time_since_last);
        }

        my_printf(UART_0_INST,"=== End Report ===\r\n");
    }
}