<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\ncontroller.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\ncontroller.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Fri Aug  1 11:27:22 2025
<BR><P>
<H3>Maximum Stack Usage =        384 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
 <LI><a href="#[5]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">SysTick_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1e]">__main</a> from __main.o(!!!main) referenced from startup_mspm0g350x_uvision.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[1f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[21]"></a>__scatterload_rt2</STRONG> (Thumb, 74 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[88]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[89]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[8a]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[8b]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[25]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[8c]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[8d]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[8e]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[8f]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[90]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[91]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[92]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[93]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[94]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[95]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[96]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[97]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[98]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[99]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[9a]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[9b]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[9c]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[9d]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[9e]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[9f]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[a0]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[a1]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[2a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[a2]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[a3]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[a4]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[a5]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[a6]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[a7]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[a8]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[20]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[a9]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[22]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[24]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[aa]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[26]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 384 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ab]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[37]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[29]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[ac]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[2b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[ad]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, aeabi_sdiv.o(.text), UNUSED)

<P><STRONG><a name="[ae]"></a>__aeabi_uidivmod</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, aeabi_sdiv.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__aeabi_idiv</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, aeabi_sdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_idiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[7d]"></a>__aeabi_idivmod</STRONG> (Thumb, 338 bytes, Stack size 8 bytes, aeabi_sdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_idivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[af]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[b0]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[b1]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ffixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[b2]"></a>_ffix</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ffixi.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>__aeabi_i2f_normalise</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[2e]"></a>__aeabi_i2f</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[b3]"></a>_fflt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[30]"></a>__aeabi_ui2f</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b4]"></a>_ffltu</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>__fpl_fcmp_InfNaN</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, fcmpin.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[23]"></a>__user_setup_stackheap</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[28]"></a>exit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_call_atexit_fns (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[32]"></a>__fpl_cmpreturn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, cmpret.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[33]"></a>__fpl_fcheck_NaN2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, fnan2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[b5]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[34]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[b6]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[38]"></a>__fpl_return_NaN</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, retnan.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
</UL>

<P><STRONG><a name="[2c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[b7]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[54]"></a>DL_Common_delayCycles</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[b9]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>Draw_Logo</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, drv_oled.o(.text.Draw_Logo))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = Draw_Logo &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[41]"></a>LCD_P6x8Char</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, drv_oled.o(.text.LCD_P6x8Char))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[42]"></a>LCD_P6x8Str</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, drv_oled.o(.text.LCD_P6x8Str))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = LCD_P6x8Str &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_string
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[43]"></a>LCD_clear_L</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, drv_oled.o(.text.LCD_clear_L))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = LCD_clear_L &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[45]"></a>OLED_Fill</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, drv_oled.o(.text.OLED_Fill))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Fill &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[3c]"></a>OLED_Set_Pos</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, drv_oled.o(.text.OLED_Set_Pos))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>

<P><STRONG><a name="[44]"></a>OLED_WrCmd</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, drv_oled.o(.text.OLED_WrCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>

<P><STRONG><a name="[3d]"></a>OLED_WrDat</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, drv_oled.o(.text.OLED_WrDat))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_WrDat &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
</UL>

<P><STRONG><a name="[49]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[4e]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[50]"></a>SYSCFG_DL_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[51]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_Common_delayCycles
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[55]"></a>_bsp_analog_i2c_ack</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, soft_i2c.o(.text._bsp_analog_i2c_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _bsp_analog_i2c_ack &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
</UL>

<P><STRONG><a name="[59]"></a>_bsp_analog_i2c_nack</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, soft_i2c.o(.text._bsp_analog_i2c_nack))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _bsp_analog_i2c_nack &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
</UL>

<P><STRONG><a name="[5a]"></a>_bsp_analog_i2c_read_byte</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, soft_i2c.o(.text._bsp_analog_i2c_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _bsp_analog_i2c_read_byte &rArr; _i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_in
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
</UL>

<P><STRONG><a name="[5c]"></a>_bsp_analog_i2c_send_byte_nask</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, soft_i2c.o(.text._bsp_analog_i2c_send_byte_nask))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _bsp_analog_i2c_send_byte_nask &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
</UL>

<P><STRONG><a name="[5d]"></a>_bsp_analog_i2c_start</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, soft_i2c.o(.text._bsp_analog_i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _bsp_analog_i2c_start &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
</UL>

<P><STRONG><a name="[40]"></a>_bsp_analog_i2c_stop</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, soft_i2c.o(.text._bsp_analog_i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _bsp_analog_i2c_stop &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Configuration
</UL>

<P><STRONG><a name="[5e]"></a>_bsp_analog_i2c_wait_ack</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, soft_i2c.o(.text._bsp_analog_i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = _bsp_analog_i2c_wait_ack &rArr; _bsp_analog_i2c_stop &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_in
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_analog_i2c_delay
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
</UL>

<P><STRONG><a name="[5b]"></a>_i2c_sda_in</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, soft_i2c.o(.text._i2c_sda_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_read_byte
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
</UL>

<P><STRONG><a name="[56]"></a>_i2c_sda_out</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, soft_i2c.o(.text._i2c_sda_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_nack
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
</UL>

<P><STRONG><a name="[62]"></a>bsp_analog_i2c_init</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, drv_oled.o(.text.bsp_analog_i2c_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bsp_analog_i2c_init &rArr; bsp_analog_i2c_stop &rArr; i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[47]"></a>bsp_analog_i2c_send_byte</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, drv_oled.o(.text.bsp_analog_i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[46]"></a>bsp_analog_i2c_start</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, drv_oled.o(.text.bsp_analog_i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = bsp_analog_i2c_start &rArr; i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[48]"></a>bsp_analog_i2c_stop</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, drv_oled.o(.text.bsp_analog_i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bsp_analog_i2c_stop &rArr; i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_init
</UL>

<P><STRONG><a name="[68]"></a>bsp_analog_i2c_wait_ack</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, drv_oled.o(.text.bsp_analog_i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_in
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
</UL>

<P><STRONG><a name="[6b]"></a>display_6_8_number</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, drv_oled.o(.text.display_6_8_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6d]"></a>display_6_8_string</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, drv_oled.o(.text.display_6_8_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = display_6_8_string &rArr; LCD_P6x8Str &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[75]"></a>gpio_input_check_channel_12_linewidth_10mm</STRONG> (Thumb, 828 bytes, Stack size 12 bytes, gray_detection.o(.text.gpio_input_check_channel_12_linewidth_10mm))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = gpio_input_check_channel_12_linewidth_10mm
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[76]"></a>gpio_input_check_channel_12_linewidth_20mm</STRONG> (Thumb, 672 bytes, Stack size 8 bytes, gray_detection.o(.text.gpio_input_check_channel_12_linewidth_20mm))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = gpio_input_check_channel_12_linewidth_20mm
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6e]"></a>i2c_CheckDevice</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, soft_i2c.o(.text.i2c_CheckDevice))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = i2c_CheckDevice &rArr; _bsp_analog_i2c_wait_ack &rArr; _bsp_analog_i2c_stop &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[69]"></a>i2c_sda_in</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, drv_oled.o(.text.i2c_sda_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
</UL>

<P><STRONG><a name="[64]"></a>i2c_sda_out</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_oled.o(.text.i2c_sda_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[27]"></a>main</STRONG> (Thumb, 460 bytes, Stack size 48 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = main &rArr; display_6_8_number &rArr; write_6_8_number &rArr; LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_togglePins
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_check_channel_12_linewidth_20mm
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_check_channel_12_linewidth_10mm
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pca9555_read_bit12
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_string
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_number
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_clear_L
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[72]"></a>oled_init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, drv_oled.o(.text.oled_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = oled_init &rArr; ssd1306_begin &rArr; ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Draw_Logo
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Fill
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_init
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[74]"></a>pca9555_read_bit12</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, nchd12.o(.text.pca9555_read_bit12))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = pca9555_read_bit12 &rArr; _bsp_analog_i2c_wait_ack &rArr; _bsp_analog_i2c_stop &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_read_byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_nack
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_ack
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>ssd1306_begin</STRONG> (Thumb, 316 bytes, Stack size 40 bytes, ssd1306.o(.text.ssd1306_begin))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ssd1306_begin &rArr; ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[78]"></a>ssd1306_command</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ssd1306.o(.text.ssd1306_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
</UL>

<P><STRONG><a name="[6c]"></a>write_6_8_number</STRONG> (Thumb, 1120 bytes, Stack size 152 bytes, drv_oled.o(.text.write_6_8_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = write_6_8_number &rArr; LCD_P6x8Char &rArr; OLED_Set_Pos &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idiv
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Char
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_P6x8Str
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;display_6_8_number
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_fcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgeq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[80]"></a>_fgeq</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgeq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[7e]"></a>__aeabi_fcmplt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fls))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmplt
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[82]"></a>_fls</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fls), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[ba]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd), UNUSED)

<P><STRONG><a name="[84]"></a>_fadd</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[81]"></a>_fcmpge</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fgef.o(x$fpl$fgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgeq
</UL>

<P><STRONG><a name="[bb]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)

<P><STRONG><a name="[83]"></a>_fcmple</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fls
</UL>

<P><STRONG><a name="[7f]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[bc]"></a>_fmul</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)

<P><STRONG><a name="[7b]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;write_6_8_number
</UL>

<P><STRONG><a name="[86]"></a>_fsub</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[63]"></a>delay_ms</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, drv_oled.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = delay_ms &rArr; DL_Common_delayCycles
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_init
</UL>

<P><STRONG><a name="[65]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_oled.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[66]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_oled.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[67]"></a>analog_i2c_delay</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, drv_oled.o(.text.analog_i2c_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[70]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, drv_oled.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>

<P><STRONG><a name="[71]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_oled.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>

<P><STRONG><a name="[6f]"></a>DL_GPIO_initDigitalInputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, drv_oled.o(.text.DL_GPIO_initDigitalInputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_in
</UL>

<P><STRONG><a name="[6a]"></a>DL_GPIO_readPins</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, drv_oled.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
</UL>

<P><STRONG><a name="[60]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, soft_i2c.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
</UL>

<P><STRONG><a name="[61]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, soft_i2c.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_out
</UL>

<P><STRONG><a name="[5f]"></a>DL_GPIO_initDigitalInputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, soft_i2c.o(.text.DL_GPIO_initDigitalInputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_i2c_sda_in
</UL>

<P><STRONG><a name="[3f]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, soft_i2c.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_read_byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_nack
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_ack
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Configuration
</UL>

<P><STRONG><a name="[58]"></a>_analog_i2c_delay</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, soft_i2c.o(.text._analog_i2c_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = _analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_read_byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_nack
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_ack
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
</UL>

<P><STRONG><a name="[57]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, soft_i2c.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_read_byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_send_byte_nask
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_nack
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_ack
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_wait_ack
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_start
</UL>

<P><STRONG><a name="[3e]"></a>I2C_GPIO_Configuration</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, soft_i2c.o(.text.I2C_GPIO_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = I2C_GPIO_Configuration &rArr; _bsp_analog_i2c_stop &rArr; _i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_bsp_analog_i2c_stop
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_CheckDevice
</UL>

<P><STRONG><a name="[73]"></a>DL_GPIO_togglePins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, main.o(.text.DL_GPIO_togglePins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_togglePins
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[52]"></a>DL_GPIO_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[53]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[4a]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[4b]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[4c]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[4d]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[4f]"></a>DL_SYSCTL_setBORThreshold</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[39]"></a>DL_SYSCTL_setSYSOSCFreq</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[3a]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
</UL>

<P><STRONG><a name="[87]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[85]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[36]"></a>_call_atexit_fns</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>
<HR></body></html>
