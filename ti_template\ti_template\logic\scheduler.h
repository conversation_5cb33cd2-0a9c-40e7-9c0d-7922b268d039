#ifndef __SCHEDULER_H
#define __SCHEDULER_H

#include "bsp_system.h"

/**
 * @brief 调度器初始化
 * 
 * @param none
 * 
 * @return none
 */
void scheduler_init(void);
/**
 * @brief 调度器运行函数
 * 
 * @param none
 * 
 * @return none
 */
void scheduler_run(void);
/**
 * @brief DL库延时函数(ms)
 * @param none
 * @return none
 */
void DL_Delay(uint32_t delayMs);

/**
 * @brief 任务状态监控函数
 * @param none
 * @return none
 */
void scheduler_monitor(void);
#endif