{"configurations": [{"name": "nchd12", "includePath": ["c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\source\\third_party\\CMSIS\\Core\\Include", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\source", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\ndrivers", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\source\\ti\\driverlib", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\source\\ti\\driverlib\\m0p", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\source\\ti\\driverlib\\m0p\\sysctl", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）", "c:\\Users\\<USER>\\Desktop\\m0\\NCHD1灰度传感器参考资料（多路通用）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\NCHD12通过I2C方式读取数据资料与例程（20241205）\\I2C读取单个12路灰度传感器例程\\12路灰度传感器检测（MSPM0G3507）\\keil"], "defines": ["__MSPM0G3507__", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "__GNUC__=4", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1"], "intelliSenseMode": "${default}"}], "version": 4}