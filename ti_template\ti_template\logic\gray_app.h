#ifndef __GRAY_APP_H
#define __GRAY_APP_H

#include "bsp_system.h"

/**
 * @brief 灰度初始化函数
 *
 * @param none
 *
 * @return none
 */
void user_gray_init(void);
void user_gray_task(void);

/**
 * @brief 12路传感器初始化函数
 *
 * @param none
 *
 * @return none
 */
void user_gray_init_12channel(void);

/**
 * @brief 12路传感器任务函数
 *
 * @param none
 *
 * @return none
 */
void user_gray_task_12channel(void);

/**
 * @brief 12路传感器线位置计算函数
 *
 * @param digital_state 12位传感器数字状态
 *
 * @return float 线位置值（范围-11到11）
 */
float calculate_line_position_12channel(uint16_t digital_state);

/**
 * @brief 线位置范围映射函数
 *
 * @param position_12ch 12路传感器位置值（-11到11）
 *
 * @return float 映射后的位置值（与7路传感器兼容）
 */
float map_position_to_7channel_range(float position_12ch);

/**
 * @brief 权重对称性验证函数
 *
 * @param position 位置值
 *
 * @return uint8_t 1表示对称，0表示不对称
 */
uint8_t verify_weight_symmetry(float position);

/**
 * @brief 一阶低通滤波函数
 *
 * @param new_value 新的输入值
 * @param old_value 上一次的滤波值
 * @param alpha 滤波系数（0-1）
 *
 * @return float 滤波后的值
 */
float low_pass_filter(float new_value, float old_value, float alpha);

/**
 * @brief 配置PCA9555芯片寄存器
 *
 * @param none
 *
 * @return uint8_t 1表示成功，0表示失败
 */
uint8_t configure_pca9555_registers(void);

/**
 * @brief 传感器自检功能
 *
 * @param none
 *
 * @return uint8_t 1表示成功，0表示失败
 */
uint8_t sensor_self_test(void);

/**
 * @brief 12路传感器单元测试函数
 *
 * @param none
 *
 * @return uint8_t 1表示测试通过，0表示测试失败
 */
uint8_t unit_test_12channel_sensor(void);

/**
 * @brief 集成测试函数
 *
 * @param none
 *
 * @return uint8_t 1表示测试通过，0表示测试失败
 */
uint8_t integration_test_pid_system(void);

/**
 * @brief 性能对比测试函数
 *
 * @param none
 *
 * @return none
 */
void performance_comparison_test(void);

/**
 * @brief 循迹稳定性测试函数
 *
 * @param none
 *
 * @return uint8_t 1表示稳定，0表示不稳定
 */
uint8_t tracking_stability_test(void);

/**
 * @brief 综合测试和验证函数
 *
 * @param none
 *
 * @return uint8_t 1表示所有测试通过，0表示有测试失败
 */
uint8_t comprehensive_test_and_validation(void);
#endif