Dependencies for Project 'nchd12', Target 'nchd12': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\source\ti\driverlib\dl_adc12.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MMD)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_aes.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MMD)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_aesadv.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MMD)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_common.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MMD)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_crc.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MMD)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_crcp.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MMD)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_dac12.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MMD)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_dma.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MMD)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_flashctl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MMD)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_i2c.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MMD)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_keystorectl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MMD)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_lcd.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MMD)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_lfss.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MMD)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_mathacl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MMD)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_mcan.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MMD)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_opa.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MMD)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_rtc_common.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MMD)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_spi.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MMD)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_timer.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MMD)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
F (..\source\ti\driverlib\dl_trng.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MMD)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_uart.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MMD)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
F (..\source\ti\driverlib\dl_vref.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MMD)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\source\ti\driverlib\m0p\dl_interrupt.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_interrupt.o -MMD)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
F (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_sysctl_mspm0g1x0x_g3x0x.o -MMD)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\ndrivers\drv_oled.c)(0x66EC4052)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/drv_oled.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\ndrivers\ssd1306.h)(0x663F09A0)
I (..\ndrivers\drv_oled.h)(0x663F09A0)
F (..\ndrivers\glcdfont.c)(0x663F09A0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/glcdfont.o -MMD)
F (..\ndrivers\ssd1306.c)(0x6655F656)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ssd1306.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\ndrivers\binary.h)(0x663F09A0)
I (..\ndrivers\drv_oled.h)(0x663F09A0)
I (..\ndrivers\ssd1306.h)(0x663F09A0)
I (..\ndrivers\glcdfont.c)(0x663F09A0)
F (..\ndrivers\soft_i2c.c)(0x674D1448)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/soft_i2c.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\ndrivers\soft_i2c.h)(0x66EC4594)
F (..\ndrivers\nchd12.c)(0x674D1C60)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nchd12.o -MMD)
I (..\ndrivers\soft_i2c.h)(0x66EC4594)
I (..\ndrivers\nchd12.h)(0x66F2CA18)
F (..\ndrivers\gray_detection.c)(0x67514488)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/gray_detection.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\ndrivers\gray_detection.h)(0x674D1990)
F (..\main.c)(0x674D1A64)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\ndrivers\drv_oled.h)(0x663F09A0)
I (..\ndrivers\soft_i2c.h)(0x66EC4594)
I (..\ndrivers\nchd12.h)(0x66F2CA18)
I (..\ndrivers\gray_detection.h)(0x674D1990)
F (..\ncontroller.syscfg)(0x66EC4468)()
F (..\ti_msp_dl_config.c)(0x66EC4468)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../ndrivers

-D__UVISION_VERSION="541" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x66EC4468)
I (..\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF42)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_timera.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timer.h)(0x6640DF42)
I (..\source\ti\driverlib\dl_timerg.h)(0x6640DF44)
I (..\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\ti_msp_dl_config.h)(0x66EC4468)()
F (.\startup_mspm0g350x_uvision.s)(0x66EC4088)(--cpu Cortex-M0+ -g --diag_suppress=A1950W

--pd "__UVISION_VERSION SETA 541"

--pd "__MSPM0G3507__ SETA 1"

--list .\listings\startup_mspm0g350x_uvision.lst

--xref -o .\objects\startup_mspm0g350x_uvision.o

--depend .\objects\startup_mspm0g350x_uvision.d)
