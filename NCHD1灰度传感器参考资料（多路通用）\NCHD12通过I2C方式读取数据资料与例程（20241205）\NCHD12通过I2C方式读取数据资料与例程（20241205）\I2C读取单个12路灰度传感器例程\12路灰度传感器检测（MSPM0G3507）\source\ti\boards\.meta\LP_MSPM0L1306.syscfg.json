/*
 * Copyright (c) 2022 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== LP_MSPM0L1306.syscfg.json ========
 *  Board schematics: http://www.ti.com/lit/ug/slau597f/slau597f.pdf
 */

 {
    "name": "LP_MSPM0L1306",
    "displayName": "MSPM0L1306 LaunchPad",
    "device": "MSPM0L130X",
    "part": "Default",
    "package": "VQFN-32(RHB)",
    "headers": [
        {
            /* http://www.ti.com/tools-software/launchpads/boosterpacks/build.html */
            "type": "BoosterPack 40 pin",
            "default": true,
            "name": "boosterpack",
            "displayName": "BoosterPack Standard Header",
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 10 },
                    { "top": 21, "bottom": 30 },

                    { "blank": true },

                    { "top": 40, "bottom": 31 },
                    { "top": 20, "bottom": 11 }
                ]
            },
            "pins": [
                {
                    "number": 1,     /* 3.3 V */
                    "name": "3V3"
                },
                {
                    "number": 2,     /* Analog in */
                    "name": "PA25",
                    "ball": "29"
                },
                {
                    "number": 3,     /* UART RX */
                    "name": "PA9",
                    "ball": "13"
                },
                {
                    "number": 4,     /* UART TX */
                    "name": "PA8",
                    "ball": "12"
                },
                {
                    "number": 5,     /* GPIO (with interrupt) */
                    "name": "PA24",
                    "ball": "28"
                },
                {
                    "number": 6,     /* Analog in */
                    "name": "PA21",
                    "ball": "25"
                },
                {
                    "number": 7,
                    "name": "PA6",
                    "ball": "10",
                    "preferredType": ["SPI_SCLK"]
                },
                {
                    "number": 8,     /* GPIO (with interrupt) */
                    "name": "PA22",
                    "ball": "26"
                },
                {
                    "number": 9,     /* I2C SCL */
                    "name": "PA1",
                    "ball": "2",
                    "preferredType": ["I2C_SCL"]
                },
                {
                    "number": 10,    /* I2C SDA */
                    "name": "PA0",
                    "ball": "1",
                    "preferredType": ["I2C_SDA"]
                },

                {
                    "number": 21,    /* 5 V */
                    "name": "5V"
                },
                {
                    "number": 22,    /* Ground */
                    "name": "GND"
                },
                {
                    "number": 23,    /* Analog input */
                    "name": "PA15",
                    "ball": "19"
                },
                {
                    "number": 24,    /* Analog input */
                    "name": "PA16",
                    "ball": "20"
                },
                {
                    "number": 25,    /* Analog input */
                    "name": "PA17",
                    "ball": "21"
                },
                {
                    "number": 26,    /* Analog input */
                    "name": "PA18",
                    "ball": "22"
                },
                {
                    "number": 27,    /* Analog input | I2S WS */
                    "name": "NC"
                },
                {
                    "number": 28,    /* Analog input | I2S SCLX */
                    "name": "NC"
                },
                {
                    "number": 29,    /* Analog Out | I2S SDout */
                    "name": "NC",
                    "preferredType" : ["AIN"]
                },
                {
                    "number": 30,    /* Analog Out | I2S SDin */
                    "name": "NC",
                    "preferredType" : ["AIN"]
                },

                {
                    "number": 40,    /* PWM | GPIO (with interrupt) */
                    "name": "PA12",
                    "ball": "16"
                },
                {
                    "number": 39,    /* PWM | GPIO (with interrupt) */
                    "name": "PA13",
                    "ball": "17"
                },
                {
                    "number": 38,    /* PWM | GPIO (with interrupt) */
                    "name": "PA26",
                    "ball": "30"
                },
                {
                    "number": 37,    /* PWM | GPIO (with interrupt) */
                    "name": "PA27",
                    "ball": "31"
                },
                {
                    "number": 36,    /* Timer Capture | GPIO (with interrupt) */
                    "name": "PA10",
                    "ball": "14"
                },
                {
                    "number": 35,    /* Timer Capture | GPIO (with interrupt) */
                    "name": "PA11",
                    "ball": "15"
                },
                {
                    "number": 34,    /* GPIO (with interrupt) */
                    "name": "NC"
                },
                {
                    "number": 33,    /* GPIO (with interrupt) */
                    "name": "NC"
                },
                {
                    "number": 32,    /* GPIO (with interrupt) */
                    "name": "NC"
                },
                {
                    "number": 31,    /* GPIO (with interrupt) */
                    "name": "NC"
                },

                {
                    "number": 20,    /* Ground */
                    "name": "GND"
                },
                {
                    "number": 19,    /* PWM | GPIO (with interrupt) */
                    "name": "PA3",
                    "ball": "7"
                },
                {
                    "number": 18,    /* SPI CS (Wireless) | GPIO (with interrupt) */
                    "name": "PA7",
                    "ball": "11"
                },
                {
                    "number": 17,    /* GPIO */
                    "name": "PA19",
                    "ball": "23"
                },
                {
                    "number": 16,    /* Reset */
                    "name": "RST"
                },
                {
                    "number": 15,
                    "name": "PA5",
                    "ball": "9",
                    "preferredType" : ["SPI_MOSI"]
                },
                {
                    "number": 14,
                    "name": "PA4",
                    "ball": "8",
                    "preferredType" : ["SPI_MISO"]
                },
                {
                    "number": 13,    /* SPI CS (Display) | GPIO (with interrupt) */
                    "name": "PA20",
                    "ball": "24"
                },
                {
                    "number": 12,    /* SPI CS (other) | GPIO (with interrupt) */
                    "name": "PA23",
                    "ball": "27"
                },
                {
                    "number": 11,    /* GPIO (with interrupt) */
                    "name": "PA11",
                    "ball": "15"
                }
            ]
        },
        {
            "type": "MSPM0L1306 Non-Existent Edge Connector",
            "default": true,
            "name": "expansion",
            "displayName": "Display for Unused Pins",
            "displayNumbers": true,
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 1}
                ]
            },
            "pins": [
                {
                    "number": 1,
                    "name": "PA14",
                    "ball": "18"
                }
            ]
        }
    ],

    "components": {
        // "LED1_RED": {
        //     "displayName": "LaunchPad LED 1 Red",
        //     "definition": "/ti/boards/components/led.json",
        //     "connections": {
        //         "OUTPUT": "33" /* PA0 */
        //     }
        // },
        // "LED2_RED": {
        //     "displayName": "LaunchPad LED 2 Red",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "28"
        //     }
        // },
        // "LED2_GREEN": {
        //     "displayName": "LaunchPad LED 2 Green",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "29"
        //     }
        // },
        // "LED2_BLUE": {
        //     "displayName": "LaunchPad LED 2 Blue",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "21"
        //     }
        // },

        // /* symbolic links/aliases for LED portability between LaunchPads */
        // "LED": {
        //     "link" : "LED1_RED"
        // },
        // "LED_DIMMABLE": {
        //     "link" : "LED2_RED"
        // },
        // "LED0": {
        //     "link": "LED1_RED"
        // },
        // "LED1": {
        //     "link": "LED2_RED"
        // },
        // "LED2": {
        //     "link": "LED2_GREEN"
        // },
        // "LED3": {
        //     "link": "LED2_BLUE"
        // },
        // "LED0_PWM" : {
        //     "link" : "LED1"
        // },
        // "LED1_PWM" : {
        //     "link" : "LED2"
        // },
        // "LED2_PWM" : {
        //     "link" : "LED3"
        // },
        // "S1": {
        //     "displayName" : "LaunchPad Button S1",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S1 LaunchPad button with no external pull.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Down",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "22" /* PA18 */
        //     }
        // },
        // "S2": {
        //     "displayName" : "LaunchPad Button S2",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S2 LaunchPad button with no external pull.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Up",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "18" /* PA14 */
        //     }
        // },
        // "S3": {
        //     "displayName" : "LaunchPad Button S3",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S3 LaunchPad button with no external pull.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Up",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "3" /* NRST */
        //     }
        // },

        /* symbolic links/aliases for BUTTON portability between LaunchPads */
        // "BUTTON0": {
        //     "link": "S1"
        // },
        // "BUTTON1": {
        //     "link": "S2"
        // },
        // "BUTTON2": {
        //     "link": "S3"
        // },

        "XDS110UART": {
            "definition": "/ti/boards/components/xds110Uart.json",
            "connections": {
                "RXD": "6",
                "TXD": "7"
            }
        }
    }
}
