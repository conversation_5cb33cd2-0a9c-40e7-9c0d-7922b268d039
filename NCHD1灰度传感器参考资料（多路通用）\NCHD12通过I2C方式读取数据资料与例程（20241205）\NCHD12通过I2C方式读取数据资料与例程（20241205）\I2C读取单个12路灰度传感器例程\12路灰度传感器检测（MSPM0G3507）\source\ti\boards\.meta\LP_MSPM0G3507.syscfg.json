/*
 * Copyright (c) 2022 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== LP_MSPM0G3507.syscfg.json ========
 *  Board schematics: http://www.ti.com/lit/ug/slau597f/slau597f.pdf
 */

 {
    "name": "LP_MSPM0G3507",
    "displayName": "MSPM0G3507 LaunchPad",
    "device": "MSPM0G350X",
    "part": "Default",
    "package": "LQFP-64(PM)",
    "headers": [
        {
            /* http://www.ti.com/tools-software/launchpads/boosterpacks/build.html */
            "type": "BoosterPack 40 pin",
            "default": true,
            "name": "boosterpack",
            "displayName": "BoosterPack Standard Header",
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 10 },
                    { "top": 21, "bottom": 30 },

                    { "blank": true },

                    { "top": 40, "bottom": 31 },
                    { "top": 20, "bottom": 11 }
                ]
            },
            "pins": [
                {
                    "number": 1,     /* 3.3 V */
                    "name": "3V3"
                },
                {
                    "number": 2,     /* Analog in */
                    "name": "PA25",
                    "ball": "26"
                },
                {
                    "number": 3,     /* UART RX */
                    "name": "PA9",
                    "ball": "55"
                },
                {
                    "number": 4,     /* UART TX */
                    "name": "PA8",
                    "ball": "54"
                },
                {
                    "number": 5,     /* GPIO (with interrupt) */
                    "name": "PA26",
                    "ball": "30"
                },
                {
                    "number": 6,     /* Analog in */
                    "name": "PB24",
                    "ball": "23"
                },
                {
                    "number": 7,
                    "name": "PB9",
                    "ball": "61",
                    "preferredType": ["SPI_SCLK"]
                },
                {
                    "number": 8,     /* GPIO (with interrupt) */
                    "name": "PA27",
                    "ball": "31"
                },
                {
                    "number": 9,     /* I2C SCL */
                    "name": "PB2",
                    "ball": "50",
                    "preferredType": ["I2C_SCL"]
                },
                {
                    "number": 10,    /* I2C SDA */
                    "name": "PB3",
                    "ball": "51",
                    "preferredType": ["I2C_SDA"]
                },

                {
                    "number": 21,    /* 5 V */
                    "name": "5V"
                },
                {
                    "number": 22,    /* Ground */
                    "name": "GND"
                },
                {
                    "number": 23,    /* Analog input */
                    "name": "PB19",
                    "ball": "16"
                },
                {
                    "number": 24,    /* Analog input */
                    "name": "PA22",
                    "ball": "18"
                },
                {
                    "number": 25,    /* Analog input */
                    "name": "PB18",
                    "ball": "15"
                },
                {
                    "number": 26,    /* Analog input */
                    "name": "PA18",
                    "ball": "11"
                },
                {
                    "number": 27,    /* Analog input | I2S WS */
                    "name": "PA24",
                    "ball": "25"
                },
                {
                    "number": 28,    /* Analog input | I2S SCLX */
                    "name": "PA17",
                    "ball": "10"
                },
                {
                    "number": 29,    /* Analog Out | I2S SDout */
                    "name": "PA16",
                    "ball": "9",
                    "preferredType" : ["AIN"]
                },
                {
                    "number": 30,    /* Analog Out | I2S SDin */
                    "name": "PA15",
                    "ball": "8",
                    "preferredType" : ["AIN"]
                },

                {
                    "number": 40,    /* PWM | GPIO (with interrupt) */
                    "name": "PB4",
                    "ball": "52"
                },
                {
                    "number": 39,    /* PWM | GPIO (with interrupt) */
                    "name": "PB1",
                    "ball": "48"
                },
                {
                    "number": 38,    /* PWM | GPIO (with interrupt) */
                    "name": "PA28",
                    "ball": "35"
                },
                {
                    "number": 37,    /* PWM | GPIO (with interrupt) */
                    "name": "PA31",
                    "ball": "39"
                },
                {
                    "number": 36,    /* Timer Capture | GPIO (with interrupt) */
                    "name": "PB20",
                    "ball": "19"
                },
                {
                    "number": 35,    /* Timer Capture | GPIO (with interrupt) */
                    "name": "PB13",
                    "ball": "1"
                },
                {
                    "number": 34,    /* GPIO (with interrupt) */
                    "name": "PA10",
                    "ball": "56"
                },
                {
                    "number": 33,    /* GPIO (with interrupt) */
                    "name": "PA11",
                    "ball": "57"
                },
                {
                    "number": 32,    /* GPIO (with interrupt) */
                    "name": "PA12",
                    "ball": "5"
                },
                {
                    "number": 31,    /* GPIO (with interrupt) */
                    "name": "PA13",
                    "ball": "6"
                },

                {
                    "number": 20,    /* Ground */
                    "name": "GND"
                },
                {
                    "number": 19,    /* PWM | GPIO (with interrupt) */
                    "name": "PB12",
                    "ball": "64"
                },
                {
                    "number": 18,    /* SPI CS (Wireless) | GPIO (with interrupt) */
                    "name": "PB17",
                    "ball": "14"
                },
                {
                    "number": 17,    /* GPIO */
                    "name": "PB15",
                    "ball": "3"
                },
                {
                    "number": 16,    /* Reset */
                    "name": "RST"
                },
                {
                    "number": 15,
                    "name": "PB8",
                    "ball": "60",
                    "preferredType" : ["SPI_MOSI"]
                },
                {
                    "number": 14,
                    "name": "PB7",
                    "ball": "59",
                    "preferredType" : ["SPI_MISO"]
                },
                {
                    "number": 13,    /* SPI CS (Display) | GPIO (with interrupt) */
                    "name": "PB6",
                    "ball": "58"
                },
                {
                    "number": 12,    /* SPI CS (other) | GPIO (with interrupt) */
                    "name": "PB0",
                    "ball": "47"
                },
                {
                    "number": 11,    /* GPIO (with interrupt) */
                    "name": "PB16",
                    "ball": "4"
                }
            ]
        },
		{
            "type": "MSPM0G3507 J17 Edge Connector",
            "default": true,
            "name": "j17_expansion",
            "displayName": "Edge Connector for Unused Pin Access",
            "displayNumbers": true,
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 9},
                    { "top":  2, "bottom": 10}
                ]
            },
            "pins": [
                {
                    "number": 1,
                    "name": "PA2",
                    "ball": "42"
                },
                {
                    "number": 2,
                    "name": "PA3",
                    "ball": "43"
                },
                {
                    "number": 3,
                    "name": "PA4",
                    "ball": "44"
                },
                {
                    "number": 4,
                    "name": "PA5",
                    "ball": "45"
                },
                {
                    "number": 5,
                    "name": "PA6",
                    "ball": "46"
                },
                {
                    "number": 6,
                    "name": "PA7",
                    "ball": "49"
                },
                {
                    "number": 7,
                    "name": "PA14",
                    "ball": "7"
                },
                {
                    "number": 8,
                    "name": "PA21",
                    "ball": "17"
                },
                {
                    "number": 9,
                    "name": "PA1",
                    "ball": "34"
                },
                {
                    "number": 10,
                    "name": "PA23",
                    "ball": "24"
                }
            ]
        },
		{
            "type": "MSPM0G3507 J19 Edge Connector",
            "default": true,
            "name": "j19_expansion",
            "displayName": "Edge Connector for Unused Pin Access",
            "displayNumbers": true,
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 9},
                    { "top":  2, "bottom": 10}
                ]
            },
            "pins": [
                {
                    "number": 1,
                    "name": "PB5",
                    "ball": "53"
                },
                {
                    "number": 2,
                    "name": "PB10",
                    "ball": "62"
                },
                {
                    "number": 3,
                    "name": "PB11",
                    "ball": "63"
                },
                {
                    "number": 4,
                    "name": "PB21",
                    "ball": "20"
                },
                {
                    "number": 5,
                    "name": "PB22",
                    "ball": "21"
                },
                {
                    "number": 6,
                    "name": "PB23",
                    "ball": "22"
                },
                {
                    "number": 7,
                    "name": "PB25",
                    "ball": "27"
                },
                {
                    "number": 8,
                    "name": "PB26",
                    "ball": "28"
                },
                {
                    "number": 9,
                    "name": "PA0",
                    "ball": "33"
                },
                {
                    "number": 10,
                    "name": "PB27",
                    "ball": "29"
                }
            ]
        },
		{
            "type": "MSPM0G3507 J25 Edge Connector",
            "default": true,
            "name": "j25_expansion",
            "displayName": "Edge Connector for Unused Pin Access",
            "displayNumbers": true,
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 17},
                    { "top":  2, "bottom": 18}
                ]
            },
            "pins": [
                {
                    "number": 1,
                    "name": "XDS_GND"
                },
                {
                    "number": 2,
                    "name": "GND"
                },
				{
                    "number": 3,
                    "name": "XDS_VBUS"
                },
                {
                    "number": 4,
                    "name": "5V"
                },
                {
                    "number": 5,
                    "name": "XDS_VCCTARGET"
                },
                {
                    "number": 6,
                    "name": "3V3"
                },
                {
                    "number": 7,
                    "name": "XDS_TXD"
                },
                {
                    "number": 8,
                    "name": "PA11",
                    "ball": "57"
                },
                {
                    "number": 9,
                    "name": "XDS_RXD"
                },
                {
                    "number": 10,
                    "name": "PA10",
                    "ball": "56"
                },
                {
                    "number": 11,
                    "name": "XDS_RESET_OUT"
                },
                {
                    "number": 12,
                    "name": "RST"
                },
                {
                    "number": 13,
                    "name": "XDS_TMS_SWDIO"
                },
                {
                    "number": 14,
                    "name": "PA19",
                    "ball": "12"
                },
                {
                    "number": 15,
                    "name": "XDS_TCK_SWDCLK"
                },
                {
                    "number": 16,
                    "name": "PA20",
                    "ball": "13"
                },
                {
                    "number": 17,
                    "name": "BSL_INVOKE"
                },
                {
                    "number": 18,
                    "name": "PA18",
                    "ball": "11"
                }
            ]
        },
		{
            "type": "MSPM0G3507 J21 Edge Connector",
            "default": true,
            "name": "j21_expansion",
            "displayName": "Edge Connector for QEI Interface",
            "displayNumbers": true,
            "dimensions": {
                "columns": [
                    { "top":  1, "bottom": 5}
                ]
            },
            "pins": [
                {
                    "number": 1,
                    "name": "PA29",
                    "ball": "36"
                },
                {
                    "number": 2,
                    "name": "PA30",
                    "ball": "37"
                },
				{
                    "number": 3,
                    "name": "PB14",
                    "ball": "2"
                },
                {
                    "number": 4,
                    "name": "3V3"
                },
                {
                    "number": 5,
                    "name": "GND"
                }
            ]
        }
    ],

    "components": {
        // "LED1_RED": {
        //     "displayName": "LaunchPad LED 1 Red",
        //     "definition": "/ti/boards/components/led.json",
        //     "connections": {
        //         "OUTPUT": "33" /* PA0 */
        //     }
        // },
        // "LED2_RED": {
        //     "displayName": "LaunchPad LED 2 Red",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "28" /* PB26 */
        //     }
        // },
        // "LED2_GREEN": {
        //     "displayName": "LaunchPad LED 2 Green",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "29" /* PB27 */
        //     }
        // },
        // "LED2_BLUE": {
        //     "displayName": "LaunchPad LED 2 Blue",
        //     "definition": "/ti/boards/components/led_dimmable.json",
        //     "connections": {
        //         "OUTPUT": "21" /* PB22 */
        //     }
        // },

        // /* symbolic links/aliases for LED portability between LaunchPads */
        // "LED": {
        //     "link" : "LED1_RED"
        // },
        // "LED_DIMMABLE": {
        //     "link" : "LED2_RED"
        // },
        // "LED0": {
        //     "link": "LED1_RED"
        // },
        // "LED1": {
        //     "link": "LED2_RED"
        // },
        // "LED2": {
        //     "link": "LED2_GREEN"
        // },
        // "LED3": {
        //     "link": "LED2_BLUE"
        // },
        // "LED0_PWM" : {
        //     "link" : "LED1"
        // },
        // "LED1_PWM" : {
        //     "link" : "LED2"
        // },
        // "LED2_PWM" : {
        //     "link" : "LED3"
        // },

        // "S1": {
        //     "displayName" : "LaunchPad Button S1",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S1 LaunchPad button with external pull down.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Down",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "11" /* PA18 */
        //     }
        // },
        // "S2": {
        //     "displayName" : "LaunchPad Button S2",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S2 LaunchPad button with no external pull.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Up",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "20" /* PB21 */
        //     }
        // },
        // "S3": {
        //     "displayName" : "LaunchPad Button S3",
        //     "definition"  : "/ti/boards/components/button.json",
        //     "longDescription" : "S3 LaunchPad button with no external pull.",
        //     /* See schematic linked at top of file */
        //     "settings": {
        //         "DIN": {
        //             "pull": "Pull Up",
        //             "interruptTrigger": "Falling Edge"
        //         }
        //     },
        //     "connections" : {
        //         "INPUT": "38" /* NRST */
        //     }
        // },

        // /* symbolic links/aliases for BUTTON portability between LaunchPads */
        // "BUTTON0": {
        //     "link": "S1"
        // },
        // "BUTTON1": {
        //     "link": "S2"
        // },
        // "BUTTON1": {
        //     "link": "S3"
        // },

        "XDS110UART": {
            "definition": "/ti/boards/components/xds110Uart.json",
            "connections": {
                "RXD": "6",
                "TXD": "7"
            }
        }
    }
}
